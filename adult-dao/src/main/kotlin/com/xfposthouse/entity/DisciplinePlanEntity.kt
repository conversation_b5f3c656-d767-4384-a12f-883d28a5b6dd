package com.xfposthouse.entity

import com.mybatisflex.annotation.*
import java.time.LocalDateTime

@Table("plans")
data class DisciplinePlanEntity(

    @Id(keyType = KeyType.Auto)
    var id: Long? = null,

    var name: String,

    var description: String? = null,

    @Column("is_public")
    var isPublic: Boolean = false,

    @Column("user_id")
    var userId: Int,

    var type: Int,

    @Column("review_status")
    var reviewStatus: Int = 0, // 0=待审核，1=通过，2=拒绝

    @Column("created_at")
    var createdAt: LocalDateTime? = LocalDateTime.now(),

    @Column("updated_at")
    var updatedAt: LocalDateTime? = LocalDateTime.now()

)