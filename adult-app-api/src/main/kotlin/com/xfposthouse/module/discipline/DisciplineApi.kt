package com.xfposthouse.module.discipline

import cn.dev33.satoken.stp.StpUtil
import com.xfposthouse.base.BaseListParam
import com.xfposthouse.module.discipline.param.DisciplineCreateParam
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import discipline.DisciplineServer
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/discipline")
class DisciplineApi(
    private val disciplineService: DisciplineServer
) {
    // 创建
    @PostMapping("create")
    fun create(@RequestBody param: DisciplineCreateParam, @RequestHeader("token") token: String): XFResponseEntity {
        val userId = StpUtil.getLoginIdAsInt()
        val result = XFResponseEntity()
        if (userId > 0) {
            if (checkCreateParam(param)) {
                val isSuccess = param.disciplinePlanEntity?.let { disciplineService.createPlan(it) }
                if (isSuccess == true) {
                    param.disciplineNodes?.forEach { it.planId = param.disciplinePlanEntity?.id?:0 }
                    param.disciplineNodes?.let { disciplineService.createPlanNodes(it) }
                } else {
                    result.setError(XFErrorCode.SERVER_ERROR)
                }
            } else {
                result.setError(XFErrorCode.PARAM_INVALID)
            }
        } else {
            result.setError(XFErrorCode.INVALID_TOKEN)
        }
        return result
    }

    @PostMapping("planNodes")
    fun getPlanList(@RequestBody param: Map<String, Any>): XFResponseEntity {
        val result = XFResponseEntity()
        val planId = param["planId"].toString().toLong()
        val list = disciplineService.getPlanNodes(planId)
        result.result = mapOf("list" to list)
        return result
    }


    @PostMapping("planList")
    fun getPlanNodes(@RequestBody param: BaseListParam): XFResponseEntity {
        val result = XFResponseEntity()
        val list = disciplineService.getPublicPlans(page = param.page?:1, pageSize = param.pageSize?:20)
        result.result = mapOf("list" to list)
        return result
    }

    /** 检验创建参数 */
    fun checkCreateParam(param: DisciplineCreateParam): Boolean {
        if (param.disciplinePlanEntity == null) {
            return false
        }
        if (param.disciplineNodes?.isEmpty() == true) {
            return false
        }
        return true
    }

 }