server:
  port: 8086

spring:
  datasource:
    url: ****************************************************************************************************************************
    password: zqx19930304
    username: root
    driver-class-name: com.mysql.cj.jdbc.Driver
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB

logging:
  config: classpath:logback-spring.xml
  # file:
  #   path: ./logs
  # logback:
  #   rollingpolicy:
  #     max-file-size: 10MB
  #     max-history: 30
  # pattern:
  #   console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'

# 备用配置（已注释）
# server:
#   port: 8086
# spring:
#   datasource:
#     url: ***************************************************************************************************************************************************************************
#     password: zqx19930304
#     # password: Zqx19930304
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: random-32
  # 是否输出操作日志
  is-log: true