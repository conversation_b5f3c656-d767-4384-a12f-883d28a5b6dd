package discipline

import com.mybatisflex.core.query.QueryWrapper
import com.xfposthouse.entity.DisciplineNodeEntity
import com.xfposthouse.entity.DisciplinePlanEntity
import com.xfposthouse.mapper.DisciplineMapper
import com.xfposthouse.mapper.DisciplineNodeMapper
import org.springframework.stereotype.Service

@Service
class DisciplineServer(
    private val planMapper: DisciplineMapper,
    private val nodeMapper: DisciplineNodeMapper,
) {
    fun createPlan(plan: DisciplinePlanEntity): Boolean {
        return planMapper.insert(plan) > 0
    }

    fun createPlanNodes(plans: List<DisciplineNodeEntity>): Boolean {
        return nodeMapper.insertBatch(plans) > 0
    }

    fun getPublicPlans(page: Int, pageSize: Int): List<DisciplinePlanEntity> {
        val queryWrapper = QueryWrapper.create().eq("is_public", true).offset((page - 1) * pageSize).limit(pageSize)
        return planMapper.selectListByQuery(queryWrapper)
    }

    fun getPlanNodes(planId: Long): List<DisciplineNodeEntity> {
        val queryWrapper = QueryWrapper.create().eq("plan_id", planId)
        return nodeMapper.selectListByQuery(queryWrapper)
    }
}